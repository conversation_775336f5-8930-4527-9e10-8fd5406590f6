"""
API 路由包
"""

from fastapi import APIRouter

from src.api.routes import health, documents, graphs, chunks, pipeline, retrieval

# 创建主路由器
router = APIRouter()

# 注册各个模块的路由
router.include_router(health.router, tags=["健康检查"])
router.include_router(pipeline.router, prefix="/pipelines", tags=["流水线管理"])
router.include_router(documents.router, prefix="/documents", tags=["文档处理"])
router.include_router(chunks.router, prefix="/chunks", tags=["分块管理"])
router.include_router(graphs.router, prefix="/graphs", tags=["图查询"])
router.include_router(retrieval.router, tags=["智能检索"])

# 为前端兼容性添加额外的路由
router.include_router(graphs.router, prefix="/graph", tags=["图查询-兼容"])

__all__ = ["router"]
