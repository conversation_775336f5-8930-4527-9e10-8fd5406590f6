"""
知识库响应相关的 API 模式定义
"""

from datetime import datetime
from typing import List, Dict, Any, Optional

from pydantic import BaseModel, Field

from ..models.base import PaginatedResponse, BaseResponse
from ..models.knowledge_bases import KnowledgeBaseInfo, KnowledgeBaseDetail, KnowledgeBaseStats


class KnowledgeBaseCreateResponse(BaseResponse):
    """知识库创建响应"""
    data: KnowledgeBaseInfo = Field(..., description="创建的知识库信息")


class KnowledgeBaseUpdateResponse(BaseResponse):
    """知识库更新响应"""
    data: KnowledgeBaseInfo = Field(..., description="更新后的知识库信息")


class KnowledgeBaseDetailResponse(BaseResponse):
    """知识库详情响应"""
    data: KnowledgeBaseDetail = Field(..., description="知识库详细信息")


class KnowledgeBaseDeleteResponse(BaseResponse):
    """知识库删除响应"""
    data: Dict[str, Any] = Field(
        default_factory=lambda: {"deleted": True},
        description="删除结果信息"
    )


class KnowledgeBaseListResponse(BaseResponse):
    """知识库列表响应"""
    data: PaginatedResponse[KnowledgeBaseInfo] = Field(..., description="分页的知识库列表")


class KnowledgeBaseStatsResponse(BaseResponse):
    """知识库统计响应"""
    data: KnowledgeBaseStats = Field(..., description="知识库统计信息")


class KnowledgeBaseSearchResponse(BaseResponse):
    """知识库搜索响应"""
    data: PaginatedResponse[KnowledgeBaseInfo] = Field(..., description="搜索结果列表")


# 兼容性响应模型（不带BaseResponse包装）
class KnowledgeBaseListData(PaginatedResponse[KnowledgeBaseInfo]):
    """知识库列表数据（兼容性）"""
    pass


class KnowledgeBaseStatsData(KnowledgeBaseStats):
    """知识库统计数据（兼容性）"""
    pass
