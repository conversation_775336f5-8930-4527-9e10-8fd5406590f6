/**
 * 知识库管理API模块
 *
 * 提供以下功能：
 * 1. 知识库列表查询和分页
 * 2. 知识库创建、更新和删除
 * 3. 知识库统计信息获取
 * 4. 知识库详情查询
 */

import { BaseApi, ListResponse, PaginationParams } from './base'

// 类型定义
export interface KnowledgeBase {
  id: string;
  kb_name: string;
  kb_des?: string;
  doc_count: number;
  create_time: string;
  update_time?: string;
}

export interface KnowledgeBaseListParams extends PaginationParams {
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateKnowledgeBaseRequest {
  kb_name: string;
  kb_des?: string;
}

export interface UpdateKnowledgeBaseRequest {
  kb_name?: string;
  kb_des?: string;
}

export interface KnowledgeBaseStats {
  total_knowledge_bases: number;
  total_documents: number;
  total_chunks: number;
  total_entities: number;
  total_relationships: number;
}

export interface KnowledgeBaseDetail extends KnowledgeBase {
  documents?: any[];
  chunks_count?: number;
  entities_count?: number;
  relationships_count?: number;
}

/**
 * 知识库 API 类
 */
class KnowledgeBaseApi extends BaseApi {
  constructor() {
    super('/knowledge-bases')
  }

  /**
   * 获取知识库列表
   */
  async getList(params: KnowledgeBaseListParams = {}): Promise<ListResponse<KnowledgeBase>> {
    const queryParams = this.buildPaginationParams({
      page: params.page,
      pageSize: params.pageSize,
      filters: {
        search: params.search,
        sort_by: params.sortBy,
        sort_order: params.sortOrder
      }
    })
    
    return this.get<ListResponse<KnowledgeBase>>('/list', queryParams)
  }

  /**
   * 获取知识库详情
   */
  async getDetail(id: string): Promise<KnowledgeBaseDetail> {
    return this.get<KnowledgeBaseDetail>(`/${id}`)
  }

  /**
   * 创建知识库
   */
  async create(data: CreateKnowledgeBaseRequest): Promise<KnowledgeBase> {
    return this.post<KnowledgeBase>('', data)
  }

  /**
   * 更新知识库
   */
  async update(id: string, data: UpdateKnowledgeBaseRequest): Promise<KnowledgeBase> {
    return this.put<KnowledgeBase>(`/${id}`, data)
  }

  /**
   * 删除知识库
   */
  async delete(id: string): Promise<{ success: boolean; message: string }> {
    return this.delete<{ success: boolean; message: string }>(`/${id}`)
  }

  /**
   * 获取知识库统计信息
   */
  async getStats(): Promise<KnowledgeBaseStats> {
    return this.get<KnowledgeBaseStats>('/stats')
  }

  /**
   * 搜索知识库
   */
  async search(params: {
    query: string;
    page?: number;
    pageSize?: number;
  }): Promise<ListResponse<KnowledgeBase>> {
    const queryParams = this.buildPaginationParams({
      page: params.page,
      pageSize: params.pageSize,
      filters: { query: params.query }
    })
    
    return this.get<ListResponse<KnowledgeBase>>('/search', queryParams)
  }
}

// 导出单例实例
export const knowledgeBaseApi = new KnowledgeBaseApi()

// 便捷函数导出
export const {
  getList: fetchKnowledgeBases,
  getDetail: fetchKnowledgeBaseDetail,
  create: createKnowledgeBase,
  update: updateKnowledgeBase,
  delete: deleteKnowledgeBase,
  getStats: fetchKnowledgeBaseStats,
  search: searchKnowledgeBases
} = knowledgeBaseApi
