import React from 'react'
import { Card, Typography, Space, Button, Tooltip, Tag } from 'antd'
import { 
  EditOutlined, 
  DeleteOutlined, 
  DatabaseOutlined,
  FileTextOutlined,
  CalendarOutlined
} from '@ant-design/icons'
import { KnowledgeBase } from '../../api/knowledgeBaseApi'

const { Title, Text, Paragraph } = Typography

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase
  onEdit: (knowledgeBase: KnowledgeBase) => void
  onDelete: (id: string) => void
  loading?: boolean
}

const KnowledgeBaseCard: React.FC<KnowledgeBaseCardProps> = ({
  knowledgeBase,
  onEdit,
  onDelete,
  loading = false
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit(knowledgeBase)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete(knowledgeBase.id)
  }

  return (
    <Card
      hoverable
      style={{ height: '100%', minHeight: 200 }}
      bodyStyle={{ padding: 16, height: '100%', display: 'flex', flexDirection: 'column' }}
      actions={[
        <Tooltip title="编辑知识库" key="edit">
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={handleEdit}
            size="small"
          />
        </Tooltip>,
        <Tooltip title="删除知识库" key="delete">
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={handleDelete}
            loading={loading}
            size="small"
          />
        </Tooltip>
      ]}
    >
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 知识库图标和名称 */}
        <div style={{ marginBottom: 12 }}>
          <Space align="start">
            <DatabaseOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <div style={{ flex: 1 }}>
              <Title level={5} style={{ margin: 0, lineHeight: 1.2 }}>
                <Tooltip title={knowledgeBase.kb_name}>
                  {knowledgeBase.kb_name}
                </Tooltip>
              </Title>
            </div>
          </Space>
        </div>

        {/* 知识库描述 */}
        <div style={{ marginBottom: 12, flex: 1 }}>
          {knowledgeBase.kb_des ? (
            <Paragraph 
              ellipsis={{ rows: 2, tooltip: knowledgeBase.kb_des }}
              style={{ margin: 0, color: '#666', fontSize: 12 }}
            >
              {knowledgeBase.kb_des}
            </Paragraph>
          ) : (
            <Text type="secondary" style={{ fontSize: 12 }}>
              暂无描述
            </Text>
          )}
        </div>

        {/* 统计信息 */}
        <div style={{ marginBottom: 12 }}>
          <Space size="small">
            <Tag icon={<FileTextOutlined />} color="blue">
              {knowledgeBase.doc_count} 文档
            </Tag>
          </Space>
        </div>

        {/* 创建时间 */}
        <div style={{ marginTop: 'auto' }}>
          <Space size="small" style={{ fontSize: 11, color: '#999' }}>
            <CalendarOutlined />
            <Text type="secondary" style={{ fontSize: 11 }}>
              {formatDate(knowledgeBase.create_time)}
            </Text>
          </Space>
        </div>
      </div>
    </Card>
  )
}

export default KnowledgeBaseCard
