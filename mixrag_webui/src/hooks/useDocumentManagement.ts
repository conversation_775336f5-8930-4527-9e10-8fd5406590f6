import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import {
  fetchDocuments,
  fetchDocumentDetail,
  uploadDocument,
  deleteDocument,
  processDocument,
  fetchDocumentStats,
  downloadDocument
} from '../api/documentApi'
import { getChunksByDocument } from '../api/chunkApi'
import { parsePaginationResponse } from '../utils/pagination'

export const useDocumentManagement = () => {
  const [mixrag_documents, setDocuments] = useState([])
  const [loading, setLoading] = useState(false)
  const [uploadLoading, setUploadLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [uploadStatusFilter, setUploadStatusFilter] = useState(null)
  const [processStatusFilter, setProcessStatusFilter] = useState(null)
  const [selectedDocument, setSelectedDocument] = useState(null)
  const [stats, setStats] = useState(null)

  // Chunk相关状态
  const [chunks, setChunks] = useState([])
  const [chunksLoading, setChunksLoading] = useState(false)
  const [chunksTotal, setChunksTotal] = useState(0)
  const [chunksCurrentPage, setChunksCurrentPage] = useState(1)
  const [chunksPageSize, setChunksPageSize] = useState(10)

  // 获取文档列表
  const loadDocuments = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetchDocuments({
        page: currentPage,
        pageSize,
        uploadStatus: uploadStatusFilter,
        processStatus: processStatusFilter
      })

      // 使用工具函数解析分页响应
      const { data, pagination } = parsePaginationResponse(response)
      setDocuments(data)
      setTotal(pagination.total)
    } catch (error) {
      message.error(error.message || '获取文档列表失败')
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, uploadStatusFilter, processStatusFilter])

  // 获取文档详情
  const loadDocumentDetail = useCallback(async (docId) => {
    try {
      const response = await fetchDocumentDetail(docId)
      // 提取响应中的data字段
      const data = response?.success ? response.data : response
      setSelectedDocument(data)
      return data
    } catch (error) {
      message.error(error.message || '获取文档详情失败')
      throw error
    }
  }, [])

  // 获取文档的chunks
  const loadDocumentChunks = useCallback(async (docId, page = 1, pageSize = 10) => {
    setChunksLoading(true)
    try {
      const response = await getChunksByDocument(docId, {
        page,
        page_size: pageSize
      })
      
      // 适配统一响应格式
      if (response.success && response.data) {
        setChunks(response.data.records || [])
        setChunksTotal(response.data.pagination?.total || 0)
        setChunksCurrentPage(response.data.pagination?.page || 1)
        setChunksPageSize(response.data.pagination?.page_size || 10)
      } else {
        setChunks([])
        setChunksTotal(0)
        console.error('获取文档chunks失败:', response.message)
      }
      
      return response
    } catch (error) {
      message.error(error.message || '获取文档chunks失败')
      setChunks([])
      setChunksTotal(0)
    } finally {
      setChunksLoading(false)
    }
  }, [])

  // 上传文档
  const handleUploadDocument = useCallback(async (file, uploadedBy) => {
    setUploadLoading(true)
    try {
      const response = await uploadDocument(file, uploadedBy)
      // 提取响应中的data字段
      const data = response?.success ? response.data : response
      message.success('文档上传成功')
      loadDocuments() // 刷新列表
      return data
    } catch (error) {
      message.error(error.message || '上传文档失败')
      throw error
    } finally {
      setUploadLoading(false)
    }
  }, [loadDocuments])

  // 删除文档
  const handleDeleteDocument = useCallback(async (docId) => {
    try {
      await deleteDocument(docId)
      message.success('文档删除成功')
      loadDocuments() // 刷新列表
    } catch (error) {
      message.error(error.message || '删除文档失败')
    }
  }, [loadDocuments])

  // 处理文档
  const handleProcessDocument = useCallback(async (docId, forceReprocess = false) => {
    try {
      await processDocument(docId, forceReprocess)
      message.success('文档处理已开始')
      loadDocuments() // 刷新列表
    } catch (error) {
      message.error(error.message || '启动文档处理失败')
    }
  }, [loadDocuments])

  // 下载文档
  const handleDownloadDocument = useCallback(async (docId, originalFilename) => {
    try {
      await downloadDocument(docId, originalFilename)
      message.success('文档下载已开始')
    } catch (error) {
      message.error(error.message || '下载文档失败')
    }
  }, [])

  // 获取统计信息
  const loadStats = useCallback(async () => {
    try {
      const response = await fetchDocumentStats()
      // 提取响应中的data字段
      const data = response?.success ? response.data : response
      setStats(data)
    } catch (error) {
      message.error(error.message || '获取统计信息失败')
    }
  }, [])

  // 重置chunks状态
  const resetChunksState = useCallback(() => {
    setChunks([])
    setChunksTotal(0)
    setChunksCurrentPage(1)
    setChunksPageSize(10)
  }, [])

  // 当筛选条件或分页参数变化时重新加载
  useEffect(() => {
    loadDocuments()
  }, [loadDocuments])

  return {
    // 数据状态
    mixrag_documents,
    loading,
    uploadLoading,
    total,
    currentPage,
    pageSize,
    uploadStatusFilter,
    processStatusFilter,
    selectedDocument,
    stats,

    // Chunk相关状态
    chunks,
    chunksLoading,
    chunksTotal,
    chunksCurrentPage,
    chunksPageSize,

    // 操作方法
    loadDocuments,
    loadDocumentDetail,
    loadDocumentChunks,
    handleUploadDocument,
    handleDeleteDocument,
    handleProcessDocument,
    handleDownloadDocument,
    loadStats,
    resetChunksState,

    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setUploadStatusFilter,
    setProcessStatusFilter,
    setSelectedDocument,
    setChunksCurrentPage,
    setChunksPageSize
  }
} 