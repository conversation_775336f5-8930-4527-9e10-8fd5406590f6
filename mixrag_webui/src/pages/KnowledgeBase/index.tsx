import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Button, 
  Space, 
  Row, 
  Col,
  Pagination,
  Statistic,
  Divider,
  Input,
  Select,
  Modal,
  Form,
  message
} from 'antd'
import { 
  PlusOutlined,
  ReloadOutlined,
  Bar<PERSON><PERSON>Outlined,
  SearchOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  NodeIndexOutlined,
  ShareAltOutlined
} from '@ant-design/icons'
import PageWrapper from '../../components/common/PageWrapper'
import KnowledgeBaseCard from '../../components/KnowledgeBase/KnowledgeBaseCard'
import { useKnowledgeBaseManagement } from '../../hooks/useKnowledgeBaseManagement'

const { Search } = Input
const { Option } = Select

const KnowledgeBaseManagement = () => {
  const [isCreateVisible, setIsCreateVisible] = useState(false)
  const [isEditVisible, setIsEditVisible] = useState(false)
  const [editingKnowledgeBase, setEditingKnowledgeBase] = useState(null)
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()

  const {
    // 数据状态
    knowledgeBases,
    loading,
    total,
    currentPage,
    pageSize,
    selectedKnowledgeBase,
    stats,
    
    // 搜索和过滤状态
    searchQuery,
    sortBy,
    sortOrder,
    
    // 操作状态
    createLoading,
    updateLoading,
    deleteLoading,
    
    // 操作方法
    loadKnowledgeBases,
    loadKnowledgeBaseDetail,
    handleCreateKnowledgeBase,
    handleUpdateKnowledgeBase,
    handleDeleteKnowledgeBase,
    loadStats,
    handleSearch,
    resetSearch,
    
    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setSortBy,
    setSortOrder
  } = useKnowledgeBaseManagement()

  // 组件挂载时加载统计信息和知识库列表
  useEffect(() => {
    console.log('🚀 知识库页面useEffect触发')
    loadStats()
    loadKnowledgeBases()

    // 测试：直接调用知识库列表API
    const testKnowledgeBaseListApi = async () => {
      try {
        console.log('🧪 直接测试知识库列表API调用')
        const response = await fetch('/api/v1/knowledge-bases/list?page=1&page_size=10')
        const data = await response.json()
        console.log('🧪 直接知识库列表API响应:', data)
      } catch (error) {
        console.error('🧪 直接知识库列表API调用失败:', error)
      }
    }
    testKnowledgeBaseListApi()

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 处理创建知识库
  const handleCreate = async (values) => {
    console.log('🚀 创建知识库表单提交:', values)
    try {
      console.log('📞 调用handleCreateKnowledgeBase...')
      await handleCreateKnowledgeBase(values)
      console.log('✅ 知识库创建成功')
      setIsCreateVisible(false)
      createForm.resetFields()
    } catch (error) {
      console.error('❌ 创建知识库失败:', error)
      // 错误已在Hook中处理
    }
  }

  // 处理编辑知识库
  const handleEdit = async (values) => {
    try {
      await handleUpdateKnowledgeBase(editingKnowledgeBase.id, values)
      setIsEditVisible(false)
      setEditingKnowledgeBase(null)
      editForm.resetFields()
    } catch {
      // 错误已在Hook中处理
    }
  }

  // 处理删除知识库
  const handleDelete = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除知识库将同时删除其中的所有文档和数据，此操作不可恢复。确定要删除吗？',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        await handleDeleteKnowledgeBase(id)
      }
    })
  }

  // 处理编辑按钮点击
  const handleEditClick = (knowledgeBase) => {
    setEditingKnowledgeBase(knowledgeBase)
    editForm.setFieldsValue({
      kb_name: knowledgeBase.kb_name,
      kb_des: knowledgeBase.kb_des
    })
    setIsEditVisible(true)
  }

  // 处理分页变化
  const handlePageChange = (page, size) => {
    setCurrentPage(page)
    setPageSize(size)
  }

  // 处理搜索
  const handleSearchChange = (value) => {
    handleSearch(value)
  }

  // 处理排序变化
  const handleSortChange = (value) => {
    const [field, order] = value.split('-')
    setSortBy(field)
    setSortOrder(order)
  }

  // 测试：硬编码统计数据
  const testStats = {
    total_knowledge_bases: 3,
    total_documents: 15,
    total_chunks: 120,
    total_entities: 45,
    total_relationships: 30,
    total_file_size: 1024000,
    avg_docs_per_kb: 5.0
  }

  // 测试：硬编码知识库数据
  const testKnowledgeBases = [
    {
      id: '1',
      name: '测试知识库1',
      description: '这是第一个测试知识库',
      created_at: '2025-01-18T10:00:00Z',
      updated_at: '2025-01-18T10:00:00Z',
      document_count: 5,
      chunk_count: 50,
      entity_count: 15,
      relationship_count: 10
    },
    {
      id: '2',
      name: '测试知识库2',
      description: '这是第二个测试知识库',
      created_at: '2025-01-18T11:00:00Z',
      updated_at: '2025-01-18T11:00:00Z',
      document_count: 8,
      chunk_count: 80,
      entity_count: 25,
      relationship_count: 18
    },
    {
      id: '3',
      name: '测试知识库3',
      description: '这是第三个测试知识库',
      created_at: '2025-01-18T12:00:00Z',
      updated_at: '2025-01-18T12:00:00Z',
      document_count: 3,
      chunk_count: 30,
      entity_count: 8,
      relationship_count: 5
    }
  ]

  return (
    <PageWrapper>
      {/* 统计信息 */}
      {(stats || testStats) && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="知识库总数"
                value={(stats || testStats).total_knowledge_bases}
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="文档总数"
                value={(stats || testStats).total_documents}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="实体总数"
                value={(stats || testStats).total_entities}
                prefix={<NodeIndexOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="关系总数"
                value={(stats || testStats).total_relationships}
                prefix={<ShareAltOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 主要内容 */}
      <Card>
        {/* 操作栏 */}
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setIsCreateVisible(true)}
              >
                创建知识库
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={() => {
                  loadKnowledgeBases()
                  loadStats()
                }}
              >
                刷新
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索知识库名称或描述"
                allowClear
                style={{ width: 250 }}
                onSearch={handleSearchChange}
                enterButton={<SearchOutlined />}
              />
              <Select
                style={{ width: 150 }}
                placeholder="排序方式"
                value={`${sortBy}-${sortOrder}`}
                onChange={handleSortChange}
              >
                <Option value="create_time-desc">创建时间 ↓</Option>
                <Option value="create_time-asc">创建时间 ↑</Option>
                <Option value="kb_name-asc">名称 A-Z</Option>
                <Option value="kb_name-desc">名称 Z-A</Option>
                <Option value="doc_count-desc">文档数 ↓</Option>
                <Option value="doc_count-asc">文档数 ↑</Option>
              </Select>
            </Space>
          </Col>
        </Row>

        <Divider />

        {/* 知识库卡片展示 - 5x2 网格 */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: '16px' }}>
          {(knowledgeBases.length > 0 ? knowledgeBases : testKnowledgeBases).map((kb) => (
            <div key={kb.id}>
              <KnowledgeBaseCard
                knowledgeBase={kb}
                onEdit={handleEditClick}
                onDelete={handleDelete}
                loading={deleteLoading}
              />
            </div>
          ))}
        </div>

        {/* 空状态 */}
        {!loading && knowledgeBases.length === 0 && testKnowledgeBases.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <DatabaseOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
            <p style={{ marginTop: 16, color: '#999' }}>
              {searchQuery ? '没有找到匹配的知识库' : '暂无知识库，点击"创建知识库"开始使用'}
            </p>
          </div>
        )}

        {/* 分页 */}
        {total > 0 && (
          <div style={{ marginTop: 24, textAlign: 'right' }}>
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={total}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
              onChange={handlePageChange}
              onShowSizeChange={handlePageChange}
              pageSizeOptions={['10', '20', '30', '50']}
            />
          </div>
        )}
      </Card>

      {/* 创建知识库弹窗 */}
      <Modal
        title="创建知识库"
        open={isCreateVisible}
        onCancel={() => {
          setIsCreateVisible(false)
          createForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreate}
        >
          <Form.Item
            name="kb_name"
            label="知识库名称"
            rules={[
              { required: true, message: '请输入知识库名称' },
              { max: 100, message: '名称长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>
          <Form.Item
            name="kb_des"
            label="知识库描述"
            rules={[
              { max: 200, message: '描述长度不能超过200个字符' }
            ]}
          >
            <Input.TextArea 
              rows={4} 
              placeholder="请输入知识库描述（可选）" 
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsCreateVisible(false)
                createForm.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={createLoading}>
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑知识库弹窗 */}
      <Modal
        title="编辑知识库"
        open={isEditVisible}
        onCancel={() => {
          setIsEditVisible(false)
          setEditingKnowledgeBase(null)
          editForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEdit}
        >
          <Form.Item
            name="kb_name"
            label="知识库名称"
            rules={[
              { required: true, message: '请输入知识库名称' },
              { max: 100, message: '名称长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>
          <Form.Item
            name="kb_des"
            label="知识库描述"
            rules={[
              { max: 200, message: '描述长度不能超过200个字符' }
            ]}
          >
            <Input.TextArea 
              rows={4} 
              placeholder="请输入知识库描述（可选）" 
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsEditVisible(false)
                setEditingKnowledgeBase(null)
                editForm.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={updateLoading}>
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </PageWrapper>
  )
}

export default KnowledgeBaseManagement
