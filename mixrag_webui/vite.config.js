import { defineConfig } from 'vite'
import path from 'path'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // 启用 React Fast Refresh
      fastRefresh: true,
    }),
    tailwindcss()
  ],

  resolve: {
    alias: {
      '@': path.resolve(new URL('.', import.meta.url).pathname, './src')
    }
  },

  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        // 添加全局变量支持
        modifyVars: {
          '@primary-color': '#1890ff',
        },
      },
    },
    // 启用 CSS 模块
    modules: {
      localsConvention: 'camelCase',
    },
  },

  // 构建配置
  build: {
    target: 'es2020',
    outDir: 'dist',
    sourcemap: true,
    // 分包策略
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          utils: ['lodash', 'dayjs'],
        },
      },
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },

  // 优化配置
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd'],
  },
  server: {
    proxy: {
      '/api/v1': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        secure: false,
      }
    }
  }
})
